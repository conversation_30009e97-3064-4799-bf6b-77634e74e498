---
type: "agent_requested"
description: "项目总结"
---
## 项目概述

这是一个基于 Next.js 14+ 的电子商务项目，采用 Monorepo 架构，支持 Web 和 H5 双端开发。项目主要面向九号公司(NineBot)的产品销售，提供完整的购物车、订单管理、用户管理等功能。

## 技术栈

### 基础架构
- **Monorepo**: 使用 pnpm workspace 管理多包项目
- **构建工具**: Turborepo 用于并行构建和缓存
- **框架**: Next.js 14+ (App Router)
- **语言**: TypeScript
- **包管理**: ppm 9.7.1+

### 前端技术
- **UI 框架**: React 18
- **组件库**: Ant Design (Web端) / Ant Design Mobile (H5端)
- **样式**: Tailwind CSS + CSS Modules
- **状态管理**: Redux Toolkit
- **表单**: React Hook Form + Yup
- **国际化**: next-intl

### 数据层
- **API**: GraphQL (使用 graphql-codegen 生成类型)
- **缓存**: Redis (RedisJSON, RedisSearch)
- **状态管理**: Redux Toolkit + RTK Query

### 开发工具
- **代码检查**: ESLint + Prettier
- **样式检查**: Stylelint
- **Git Hooks**: Husky + lint-staged
- **提交规范**: Commitlint
- **部署**: PM2 + Nginx

## 项目结构

```
ninebot-web/
├── apps/                    # 应用程序
│   ├── web/                # Web端应用 (端口3000)
│   │   ├── src/
│   │   │   ├── app/        # Next.js App Router
│   │   │   ├── components/ # 组件
│   │   │   ├── businessComponents/ # 业务组件
│   │   │   ├── hooks/      # 自定义Hooks
│   │   │   ├── store/      # Redux store
│   │   │   ├── services/   # API服务
│   │   │   ├── types/      # TypeScript类型
│   │   │   └── utils/      # 工具函数
│   │   └── package.json
│   └── h5/                 # H5端应用 (端口3001)
│       └── (类似web结构)
├── packages/               # 共享包
│   ├── core/               # 核心业务逻辑
│   │   ├── src/
│   │   │   ├── graphql/    # GraphQL查询和变更
│   │   │   ├── store/      # Redux store配置
│   │   │   ├── services/   # API服务
│   │   │   ├── utils/      # 工具函数
│   │   │   ├── hooks/      # 自定义Hooks
│   │   │   ├── i18n/       # 国际化配置
│   │   │   └── config/     # 环境配置
│   ├── eslint-config/      # ESLint配置
│   └── tailwind-config/    # Tailwind配置
└── 根配置文件
```

## 常用开发命令

### 根目录命令
```bash
# 开发模式 (启动所有应用)
pnpm dev

# 构建
pnpm build

# 代码检查
pnpm lint

# 类型检查
pnpm type-check

# 格式化代码
pnpm format

# 清理缓存
pnpm clean:cache
```

### 应用特定命令
```bash
# Web应用
cd apps/web
pnpm dev              # 开发模式 (端口3000)
pnpm build            # 构建
pnpm codegen:dev      # 生成GraphQL类型(开发环境)
pnpm codegen:prod     # 生成GraphQL类型(生产环境)

# H5应用
cd apps/h5
pnpm dev              # 开发模式 (端口3001)
pnpm build            # 构建
```

### 部署命令
```bash
# 部署到生产环境
pnpm deploy:master    # 主节点部署
pnpm deploy:slave     # 从节点部署

# PM2管理
pnpm pm2:list         # 查看进程
pnpm pm2:delete:all   # 删除所有进程
```

## 核心架构模式

### 1. 状态管理架构
- **Redux Store**: 使用Redux Toolkit管理全局状态
- **Slices**: 按功能模块划分 (user, cart, checkout, orders等)
- **RTK Query**: 用于API请求和缓存管理
- **Middleware**: 自定义中间件处理用户状态等

### 2. GraphQL集成
- **代码生成**: 使用graphql-codegen自动生成TypeScript类型
- **查询管理**: 查询和变更按功能模块分类存放
- **类型安全**: 完整的GraphQL类型支持

### 3. 国际化架构
- **多语言支持**: 支持中文和英文
- **路由国际化**: 基于Next.js App Router的[locale]动态路由
- **配置管理**: 统一的国际化配置文件

### 4. 组件架构
- **通用组件**: 存放在components/common/目录
- **业务组件**: 存放在components/business/目录
- **共享业务组件**: 存放在packages/core/src/businessComponents/目录

### 5. 环境配置
- **多环境支持**: development, staging, production
- **配置合并**: 使用lodash-es进行配置合并
- **环境检测**: 自动检测当前运行环境

## 开发注意事项

### 1. 代码规范
- 使用ESLint和Prettier进行代码格式化
- 遵循Commitlint提交规范
- 使用Stylelint进行样式检查

### 2. TypeScript
- 所有代码必须使用TypeScript
- 优先使用类型推断，必要时显式声明类型
- 导入类型时使用`import type`语法

### 3. 组件开发
- 优先使用函数组件和React Hooks
- 合理拆分组件，保持单一职责
- 使用Tailwind CSS进行样式开发

### 4. GraphQL开发
- 使用GraphQL Code Generator生成类型
- 查询和变更按功能模块分类
- 遵循GraphQL最佳实践

### 5. 状态管理
- 优先使用RTK Query进行API请求
- 合理使用Redux slices管理本地状态
- 避免过度使用全局状态

## 部署架构

### 1. 构建流程
- 使用Turborepo进行并行构建
- 自动生成GraphQL类型
- 支持环境变量注入

### 2. 部署方式
- 使用PM2进行进程管理
- 支持多节点部署
- Nginx作为反向代理

### 3. 缓存策略
- Redis缓存常用数据
- Next.js缓存静态资源
- RTK Query缓存API响应

## 特殊配置

### 1. Cursor规则
- 项目配置了Cursor开发环境的规则
- 包含项目架构和技术栈信息
- 开发时需要遵循相关规范

### 2. 构建优化
- 使用Turborepo缓存加速构建
- 支持增量构建
- 优化了依赖关系

### 3. 开发体验
- 热重载支持
- 类型检查实时反馈
- 代码格式化自动化
